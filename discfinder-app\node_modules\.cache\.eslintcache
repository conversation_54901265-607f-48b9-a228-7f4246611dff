[{"C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts": "4", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx": "6"}, {"size": 554, "mtime": 1751073000293, "results": "7", "hashOfConfig": "8"}, {"size": 425, "mtime": 1751073000018, "results": "9", "hashOfConfig": "8"}, {"size": 32556, "mtime": 1751084482984, "results": "10", "hashOfConfig": "8"}, {"size": 8945, "mtime": 1751084424543, "results": "11", "hashOfConfig": "8"}, {"size": 8006, "mtime": 1751084029771, "results": "12", "hashOfConfig": "8"}, {"size": 6258, "mtime": 1751083217163, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ncyc5o", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx", ["32"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts", ["33"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx", [], [], {"ruleId": "34", "severity": 1, "message": "35", "line": 181, "column": 26, "nodeType": "36", "messageId": "37", "endLine": 181, "endColumn": 34}, {"ruleId": "34", "severity": 1, "message": "38", "line": 192, "column": 15, "nodeType": "36", "messageId": "37", "endLine": 192, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'userRole' is assigned a value but never used.", "Identifier", "unusedVar", "'data' is assigned a value but never used."]