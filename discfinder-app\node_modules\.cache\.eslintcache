[{"C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx": "3", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts": "4", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx": "6", "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx": "7"}, {"size": 554, "mtime": 1751073000293, "results": "8", "hashOfConfig": "9"}, {"size": 425, "mtime": 1751073000018, "results": "10", "hashOfConfig": "9"}, {"size": 40181, "mtime": 1751086021556, "results": "11", "hashOfConfig": "9"}, {"size": 11802, "mtime": 1751085767875, "results": "12", "hashOfConfig": "9"}, {"size": 8006, "mtime": 1751084029771, "results": "13", "hashOfConfig": "9"}, {"size": 6258, "mtime": 1751083217163, "results": "14", "hashOfConfig": "9"}, {"size": 4364, "mtime": 1751085799612, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ncyc5o", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\App.tsx", ["37", "38", "39", "40"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\lib\\supabase.ts", ["41", "42"], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ImageUpload.tsx", [], [], "C:\\Users\\<USER>\\lostandfound\\discfinder-app\\src\\components\\ReturnStatusManager.tsx", [], [], {"ruleId": "43", "severity": 2, "message": "44", "line": 144, "column": 3, "nodeType": "45", "endLine": 144, "endColumn": 18}, {"ruleId": "46", "severity": 1, "message": "47", "line": 365, "column": 26, "nodeType": "48", "messageId": "49", "endLine": 365, "endColumn": 34}, {"ruleId": "46", "severity": 1, "message": "50", "line": 754, "column": 11, "nodeType": "48", "messageId": "49", "endLine": 754, "endColumn": 15}, {"ruleId": "46", "severity": 1, "message": "51", "line": 754, "column": 17, "nodeType": "48", "messageId": "49", "endLine": 754, "endColumn": 24}, {"ruleId": "46", "severity": 1, "message": "52", "line": 241, "column": 15, "nodeType": "48", "messageId": "49", "endLine": 241, "endColumn": 19}, {"ruleId": "46", "severity": 1, "message": "52", "line": 255, "column": 15, "nodeType": "48", "messageId": "49", "endLine": 255, "endColumn": 19}, "react-hooks/rules-of-hooks", "React Hook \"React.useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "MemberExpression", "@typescript-eslint/no-unused-vars", "'userRole' is assigned a value but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "'isGuest' is assigned a value but never used.", "'data' is assigned a value but never used."]