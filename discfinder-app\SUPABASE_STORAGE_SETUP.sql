-- Supabase Storage Setup for DiscFinder Images
-- Run this in your Supabase SQL Editor after setting up the main database schema

-- Create storage bucket for disc images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'disc-images',
  'disc-images', 
  true,
  52428800, -- 50MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to upload images
CREATE POLICY "Authenticated users can upload disc images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'disc-images' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Policy: Allow public read access to disc images
CREATE POLICY "Public read access for disc images" ON storage.objects
FOR SELECT USING (bucket_id = 'disc-images');

-- Policy: Allow users to update their own images
CREATE POLICY "Users can update their own disc images" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'disc-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Allow users to delete their own images
CREATE POLICY "Users can delete their own disc images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'disc-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Create a function to clean up orphaned images
CREATE OR REPLACE FUNCTION cleanup_orphaned_images()
RETURNS void AS $$
BEGIN
  -- Delete images that are not referenced in found_discs table
  DELETE FROM storage.objects 
  WHERE bucket_id = 'disc-images'
  AND NOT EXISTS (
    SELECT 1 FROM found_discs 
    WHERE image_urls @> ARRAY[storage.objects.name]
  )
  AND created_at < NOW() - INTERVAL '24 hours'; -- Only delete images older than 24 hours
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION cleanup_orphaned_images() TO authenticated;
